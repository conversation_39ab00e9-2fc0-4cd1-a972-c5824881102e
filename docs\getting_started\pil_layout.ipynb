import sys
import os
import warnings
from PIL import Image, ImageDraw, ImageFont

# sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..'))
warnings.filterwarnings("ignore")
os.environ["PROTOCOL_BUFFERS_PYTHON_IMPLEMENTATION"] = "python"

def visualize_ocr_boxes(image_path, ocr_result, output_path="visualized.png"):
    """
    Visualize OCR boxes using PIL ImageDraw.
    
    Args:
        image_path: Path to the original image
        ocr_result: OCR result object with texts attribute
        output_path: Path to save the annotated image
    """
    # Open the original image
    image = Image.open(image_path).convert("RGB")
    
    # Create a copy to draw on
    annotated_image = image.copy()
    draw = ImageDraw.Draw(annotated_image)
    
    # Draw bounding boxes if OCR results exist
    if hasattr(ocr_result, "texts") and ocr_result.texts:
        for item in ocr_result.texts:
            bbox = getattr(item, "bbox", None)
            if bbox and len(bbox) == 4:
                x1, y1, x2, y2 = bbox
                
                # Draw rectangle around text
                draw.rectangle(
                    [(x1, y1), (x2, y2)], 
                    outline='red', 
                    width=2
                )
    
    # Save the annotated image
    annotated_image.save(output_path)
    print(f"Visualization saved to {output_path}")


def test_ocr_extraction():
    from omnidocs.tasks.ocr_extraction.extractors.paddle import PaddleOCRExtractor
    from omnidocs.tasks.ocr_extraction.extractors.tesseract_ocr import TesseractOCRExtractor
    from omnidocs.tasks.ocr_extraction.extractors.easy_ocr import EasyOCRExtractor

    extractors = [
        PaddleOCRExtractor,
        TesseractOCRExtractor,
        EasyOCRExtractor,
    ]

    image_path = "C:\\Users\\<USER>\\OneDrive\\Desktop\\CogLab\\11-07-2025\\Omnidocs\\tests\\ocr_extraction\\assets\\invoice.jpg"
    
    for extractor_cls in extractors:
        print(f"\nTesting {extractor_cls.__name__}")
        print("-" * 40)
        
        try:
            result = extractor_cls().extract(image_path)
            print(f"Text length: {len(result.full_text)} chars")
            print(f"Preview: '{result.full_text[:100]}...'")
            
            vis_path = f"visualized_{extractor_cls.__name__}.png"
            visualize_ocr_boxes(image_path, result, vis_path)
            
            print("SUCCESS")
        except Exception as e:
            print(f"ERROR: {e}")


if __name__ == "__main__":
    test_ocr_extraction()


def visualize_ocr_boxes(image_path, ocr_result, output_path="visualized.png"):
    """
    Visualize OCR boxes using PIL ImageDraw.
    
    Args:
        image_path: Path to the original image
        ocr_result: OCR result object with texts attribute
        output_path: Path to save the annotated image
    """
    # Open the original image
    image = Image.open(image_path).convert("RGB")
    
    # Create a copy to draw on
    annotated_image = image.copy()
    draw = ImageDraw.Draw(annotated_image)
    
    # Draw bounding boxes if OCR results exist
    if hasattr(ocr_result, "texts") and ocr_result.texts:
        for item in ocr_result.texts:
            bbox = getattr(item, "bbox", None)
            if bbox and len(bbox) == 4:
                x1, y1, x2, y2 = bbox
                
                # Draw rectangle around text
                draw.rectangle(
                    [(x1, y1), (x2, y2)], 
                    outline='red', 
                    width=2
                )
    
    # Save the annotated image
    annotated_image.save(output_path)
    print(f"Visualization saved to {output_path}")
